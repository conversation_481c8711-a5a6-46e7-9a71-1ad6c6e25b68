[{
    "name": data.definition.name,
    "type":data.type
    "data": {
        "id": data.mcp_info.server_id,
        "name": data.DisplayName,
        "input_schema": data.mcp_info.input_schema,
        "output_schema": data.mcp_info.output_schema,
    },
},
{
    "name": "displayName",
    "type": data.type
}
]


[
{
    "name": "MCP_Candidate_Interview_generate_interview_agenda",
    "id": "47d735d3-cd2b-4b40-9921-e0946c94dc31",
    "type": "mcp",
    "data": {
        "display_name": "Candidate Interview - candidate_suitability_pre",
        "input_schema": {
            "type": "object",
            "properties": {
                "resume_s3_link": {
                    "type": "string",
                    "description": "Candi<PERSON>'s resume"
                },
                "job_description_s3_link": {
                    "type": "string",
                    "description": "Job description"
                }
            },
            "required": [
                "job_description_s3_link",
                "resume_s3_link"
            ]
        },
        "output_schema": {
            "type": "object",
            "properties": {
                "suitability_analysis": {
                    "type": "string",
                    "description": "Analysis of candidate's suitability for the job"
                },
                "resume_details": {
                    "type": "string",
                    "description": "Candidate's resume"
                },
                "jd_details": {
                    "type": "string",
                    "description": "Interview job description"
                }
            },
            "required": []
        }
    }
}

{
    "name": "SelectDataComponent",
    "display_name": "Select Data",
    "type": "component"
}
]