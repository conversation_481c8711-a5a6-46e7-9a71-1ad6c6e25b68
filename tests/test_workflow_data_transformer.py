"""
Test module for workflow data transformer functionality.
"""

import json
import pytest
from app.utils.workflow_data_transformer import (
    transform_workflow_data_to_expected_format,
    extract_mcp_nodes_only,
    extract_component_nodes_only,
    _should_skip_node,
    _transform_mcp_node,
    _transform_component_node
)


def load_sample_workflow_data():
    """Load sample workflow data from the sample_workflow.json file."""
    try:
        with open("sample_workflow.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        # Return a minimal sample for testing if file doesn't exist
        return {
            "nodes": [
                {
                    "id": "start-node",
                    "type": "WorkflowNode",
                    "data": {
                        "type": "component",
                        "originalType": "StartNode",
                        "definition": {
                            "name": "StartNode",
                            "display_name": "Start"
                        }
                    }
                },
                {
                    "id": "MCP_Candidate_Interview_candidate_suitability_pre-1747901636059",
                    "type": "WorkflowNode",
                    "data": {
                        "type": "mcp",
                        "originalType": "MCP_Candidate_Interview_candidate_suitability_pre",
                        "definition": {
                            "name": "MCP_Candidate_Interview_candidate_suitability_pre",
                            "display_name": "Candidate Interview - candidate_suitability_pre",
                            "mcp_info": {
                                "server_id": "47d735d3-cd2b-4b40-9921-e0946c94dc31",
                                "input_schema": {
                                    "type": "object",
                                    "properties": {
                                        "resume_s3_link": {
                                            "type": "string",
                                            "description": "Candidate's resume"
                                        },
                                        "job_description_s3_link": {
                                            "type": "string",
                                            "description": "Job description"
                                        }
                                    },
                                    "required": ["job_description_s3_link", "resume_s3_link"]
                                },
                                "output_schema": {
                                    "type": "object",
                                    "properties": {
                                        "suitability_analysis": {
                                            "type": "string",
                                            "description": "Analysis of candidate's suitability for the job"
                                        }
                                    },
                                    "required": []
                                }
                            }
                        }
                    }
                },
                {
                    "id": "SelectDataComponent-1748238994773",
                    "type": "WorkflowNode",
                    "data": {
                        "type": "component",
                        "originalType": "SelectDataComponent",
                        "definition": {
                            "name": "SelectDataComponent",
                            "display_name": "Select Data"
                        }
                    }
                }
            ]
        }


class TestWorkflowDataTransformer:
    """Test class for workflow data transformer functions."""
    
    def test_transform_workflow_data_to_expected_format(self):
        """Test the main transformation function."""
        sample_data = load_sample_workflow_data()
        result = transform_workflow_data_to_expected_format(sample_data)
        
        # Should return a list
        assert isinstance(result, list)
        
        # Should have at least 2 nodes (MCP and component, excluding StartNode)
        assert len(result) >= 2
        
        # Check that StartNode is filtered out
        node_names = [node.get("name", "") for node in result]
        assert "StartNode" not in node_names
    
    def test_extract_mcp_nodes_only(self):
        """Test extracting only MCP nodes."""
        sample_data = load_sample_workflow_data()
        result = extract_mcp_nodes_only(sample_data)
        
        # Should return a list
        assert isinstance(result, list)
        
        # All nodes should be MCP type
        for node in result:
            assert node.get("type") == "mcp"
            assert "id" in node
            assert "name" in node
            assert "data" in node
            assert "display_name" in node["data"]
            assert "input_schema" in node["data"]
            assert "output_schema" in node["data"]
    
    def test_extract_component_nodes_only(self):
        """Test extracting only component nodes."""
        sample_data = load_sample_workflow_data()
        result = extract_component_nodes_only(sample_data)
        
        # Should return a list
        assert isinstance(result, list)
        
        # All nodes should be component type
        for node in result:
            assert node.get("type") == "component"
            assert "name" in node
            assert "display_name" in node
    
    def test_should_skip_node(self):
        """Test the node skipping logic."""
        # StartNode should be skipped
        start_node = {
            "data": {
                "originalType": "StartNode"
            }
        }
        assert _should_skip_node(start_node) is True
        
        # MCP node should not be skipped
        mcp_node = {
            "data": {
                "originalType": "MCP_Candidate_Interview_candidate_suitability_pre"
            }
        }
        assert _should_skip_node(mcp_node) is False
        
        # Component node should not be skipped
        component_node = {
            "data": {
                "originalType": "SelectDataComponent"
            }
        }
        assert _should_skip_node(component_node) is False
    
    def test_transform_mcp_node(self):
        """Test MCP node transformation."""
        mcp_node = {
            "data": {
                "definition": {
                    "name": "MCP_Test_Node",
                    "display_name": "Test MCP Node",
                    "mcp_info": {
                        "server_id": "test-server-id",
                        "input_schema": {"type": "object"},
                        "output_schema": {"type": "object"}
                    }
                }
            }
        }
        
        result = _transform_mcp_node(mcp_node)
        
        assert result["name"] == "MCP_Test_Node"
        assert result["id"] == "test-server-id"
        assert result["type"] == "mcp"
        assert result["data"]["display_name"] == "Test MCP Node"
        assert "input_schema" in result["data"]
        assert "output_schema" in result["data"]
    
    def test_transform_component_node(self):
        """Test component node transformation."""
        component_node = {
            "data": {
                "definition": {
                    "name": "TestComponent",
                    "display_name": "Test Component"
                }
            }
        }
        
        result = _transform_component_node(component_node)
        
        assert result["name"] == "TestComponent"
        assert result["display_name"] == "Test Component"
        assert result["type"] == "component"
    
    def test_empty_workflow_data(self):
        """Test handling of empty workflow data."""
        # Empty dict
        result = transform_workflow_data_to_expected_format({})
        assert result == []
        
        # No nodes key
        result = transform_workflow_data_to_expected_format({"other_key": "value"})
        assert result == []
        
        # Empty nodes list
        result = transform_workflow_data_to_expected_format({"nodes": []})
        assert result == []
    
    def test_malformed_node_data(self):
        """Test handling of malformed node data."""
        malformed_data = {
            "nodes": [
                {
                    "id": "malformed-node",
                    # Missing data field
                },
                {
                    "id": "another-malformed",
                    "data": {
                        # Missing type field
                        "definition": {}
                    }
                }
            ]
        }
        
        result = transform_workflow_data_to_expected_format(malformed_data)
        # Should handle gracefully and return empty list or skip malformed nodes
        assert isinstance(result, list)
