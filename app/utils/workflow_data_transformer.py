"""
Utility module for transforming workflow data from sample_workflow.json format
to the expected output format for workflow processing.
"""

from typing import Dict, Any, List


def transform_workflow_data_to_expected_format(workflow_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Transform workflow data from the sample_workflow.json schema to the expected output format.

    This function takes the workflow_data (incoming from request) and builds the output
    like expected_json.json format.

    Args:
        workflow_data: The workflow data in the format from sample_workflow.json

    Returns:
        List of transformed nodes in the expected format

    Example:
        For MCP nodes:
        {
            "name": "MCP_Candidate_Interview_generate_interview_agenda",
            "id": "47d735d3-cd2b-4b40-9921-e0946c94dc31",
            "type": "mcp",
            "data": {
                "display_name": "Candidate Interview - candidate_suitability_pre",
                "input_schema": {...},
                "output_schema": {...}
            }
        }

        For component nodes:
        {
            "name": "SelectDataComponent",
            "display_name": "Select Data",
            "type": "component"
        }
    """

    if not workflow_data or "nodes" not in workflow_data:
        return []

    nodes = workflow_data.get("nodes", [])
    transformed_nodes = []

    for node in nodes:
        # Skip StartNode and other system nodes
        if _should_skip_node(node):
            continue

        # Extract basic node information
        node_id = node.get("id", "")
        node_data = node.get("data", {})
        node_type = node_data.get("type", "")

        # Transform based on node type
        if node_type == "mcp":
            transformed_node = _transform_mcp_node(node)
        elif node_type == "component":
            transformed_node = _transform_component_node(node)
        else:
            # Skip unknown node types
            continue

        if transformed_node:
            transformed_nodes.append(transformed_node)

    return transformed_nodes


def _should_skip_node(node: Dict[str, Any]) -> bool:
    """
    Determine if a node should be skipped during transformation.

    Args:
        node: The node to check

    Returns:
        True if the node should be skipped, False otherwise
    """
    node_data = node.get("data", {})
    original_type = node_data.get("originalType", "")

    # Skip StartNode and other system nodes
    skip_types = ["StartNode", "EndNode"]

    return original_type in skip_types


def _transform_mcp_node(node: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform an MCP node to the expected format.

    Args:
        node: The MCP node from workflow data

    Returns:
        Transformed MCP node in expected format
    """
    node_data = node.get("data", {})
    definition = node_data.get("definition", {})
    mcp_info = definition.get("mcp_info", {})

    # Extract required fields
    node_name = definition.get("name", "")
    server_id = mcp_info.get("server_id", "")
    display_name = definition.get("display_name", "")
    input_schema = mcp_info.get("input_schema", {})
    output_schema = mcp_info.get("output_schema", {})

    return {
        "name": node_name,
        "id": server_id,
        "type": "mcp",
        "data": {
            "display_name": display_name,
            "input_schema": input_schema,
            "output_schema": output_schema
        }
    }


def _transform_component_node(node: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform a component node to the expected format.

    Args:
        node: The component node from workflow data

    Returns:
        Transformed component node in expected format
    """
    node_data = node.get("data", {})
    definition = node_data.get("definition", {})

    # Extract required fields
    node_name = definition.get("name", "")
    display_name = definition.get("display_name", "")

    return {
        "name": node_name,
        "display_name": display_name,
        "type": "component"
    }


def extract_mcp_nodes_only(workflow_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Extract only MCP nodes from workflow data.

    Args:
        workflow_data: The workflow data in the format from sample_workflow.json

    Returns:
        List of MCP nodes in the expected format
    """
    all_nodes = transform_workflow_data_to_expected_format(workflow_data)
    return [node for node in all_nodes if node.get("type") == "mcp"]


def extract_component_nodes_only(workflow_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Extract only component nodes from workflow data.

    Args:
        workflow_data: The workflow data in the format from sample_workflow.json

    Returns:
        List of component nodes in the expected format
    """
    all_nodes = transform_workflow_data_to_expected_format(workflow_data)
    return [node for node in all_nodes if node.get("type") == "component"]


def extract_mcp_and_component_nodes_as_dict_list(workflow_data: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    Extract all nodes that are of type 'mcp' or 'component' from workflow_data
    and return them as a List[Dict[str, str]].

    Args:
        workflow_data: The workflow data in the format from sample_workflow.json

    Returns:
        List[Dict[str, str]] containing all MCP and component nodes with string values only

    Example output:
        [
            {
                "id": "47d735d3-cd2b-4b40-9921-e0946c94dc31",
                "name": "MCP_Candidate_Interview_generate_interview_agenda",
                "type": "mcp",
                "display_name": "Candidate Interview - generate_interview_agenda"
            },
            {
                "id": "SelectDataComponent-1748238994773",
                "name": "SelectDataComponent",
                "type": "component",
                "display_name": "Select Data"
            }
        ]
    """

    if not workflow_data or "nodes" not in workflow_data:
        return []

    nodes = workflow_data.get("nodes", [])
    result_nodes = []

    for node in nodes:
        # Skip StartNode and other system nodes
        if _should_skip_node(node):
            continue

        # Extract basic node information
        node_id = node.get("id", "")
        node_data = node.get("data", {})
        node_type = node_data.get("type", "")

        # Only process MCP and component nodes
        if node_type not in ["mcp", "component"]:
            continue

        # Extract common fields
        definition = node_data.get("definition", {})
        node_name = definition.get("name", "")
        display_name = definition.get("display_name", "")

        # Create the dictionary with string values only
        node_dict = {
            "id": str(node_id),
            "name": str(node_name),
            "type": str(node_type),
            "display_name": str(display_name)
        }

        # For MCP nodes, we might want to include server_id as well
        if node_type == "mcp":
            mcp_info = definition.get("mcp_info", {})
            server_id = mcp_info.get("server_id", "")
            if server_id:
                node_dict["server_id"] = str(server_id)

        result_nodes.append(node_dict)

    return result_nodes


def extract_mcp_and_component_nodes_simple(workflow_data: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    Simplified version that extracts only essential fields as strings.

    Args:
        workflow_data: The workflow data in the format from sample_workflow.json

    Returns:
        List[Dict[str, str]] with minimal fields: id, name, type
    """

    if not workflow_data or "nodes" not in workflow_data:
        return []

    nodes = workflow_data.get("nodes", [])
    result_nodes = []

    for node in nodes:
        # Skip StartNode and other system nodes
        if _should_skip_node(node):
            continue

        # Extract basic node information
        node_id = node.get("id", "")
        node_data = node.get("data", {})
        node_type = node_data.get("type", "")

        # Only process MCP and component nodes
        if node_type not in ["mcp", "component"]:
            continue

        # Extract node name
        definition = node_data.get("definition", {})
        node_name = definition.get("name", "")

        # Create simple dictionary with string values only
        node_dict = {
            "id": str(node_id),
            "name": str(node_name),
            "type": str(node_type)
        }

        result_nodes.append(node_dict)

    return result_nodes
