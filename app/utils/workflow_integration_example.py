"""
Example integration of workflow data transformation in a createWorkflow function.

This shows how to use the workflow_data_transformer in the context of 
processing workflow creation requests.
"""

import json
from typing import Dict, Any, List
from app.utils.workflow_data_transformer import transform_workflow_data_to_expected_format


def example_create_workflow_with_transformation(request_workflow_data: str) -> Dict[str, Any]:
    """
    Example function showing how to integrate the workflow data transformation
    in a createWorkflow function.
    
    This function demonstrates the pattern:
    1. Parse the incoming workflow_data JSON string
    2. Transform it to the expected format
    3. Process the transformed data as needed
    
    Args:
        request_workflow_data: JSON string containing workflow data from request
        
    Returns:
        Dictionary containing the processing results
    """
    
    try:
        # Step 1: Parse workflow_data from string to JSON
        # This is equivalent to: workflow_data = json.loads(request.workflow_data)
        print("[DEBUG] Attempting to parse workflow_data JSON")
        workflow_data = json.loads(request_workflow_data)
        print("[DEBUG] Successfully parsed workflow_data JSON")
        
        # Step 2: Transform the workflow data to expected format
        print("[DEBUG] Transforming workflow data to expected format")
        transformed_nodes = transform_workflow_data_to_expected_format(workflow_data)
        print(f"[DEBUG] Transformed {len(transformed_nodes)} nodes")
        
        # Step 3: Process the transformed data
        # Here you can work with the transformed nodes in the expected format
        
        # Example: Separate MCP and component nodes
        mcp_nodes = [node for node in transformed_nodes if node.get("type") == "mcp"]
        component_nodes = [node for node in transformed_nodes if node.get("type") == "component"]
        
        print(f"[DEBUG] Found {len(mcp_nodes)} MCP nodes and {len(component_nodes)} component nodes")
        
        # Example: Extract specific information from MCP nodes
        mcp_info = []
        for mcp_node in mcp_nodes:
            mcp_data = {
                "name": mcp_node.get("name"),
                "id": mcp_node.get("id"),
                "display_name": mcp_node.get("data", {}).get("display_name"),
                "input_schema": mcp_node.get("data", {}).get("input_schema"),
                "output_schema": mcp_node.get("data", {}).get("output_schema")
            }
            mcp_info.append(mcp_data)
        
        # Example: Extract specific information from component nodes
        component_info = []
        for comp_node in component_nodes:
            comp_data = {
                "name": comp_node.get("name"),
                "display_name": comp_node.get("display_name"),
                "type": comp_node.get("type")
            }
            component_info.append(comp_data)
        
        # Step 4: Continue with your workflow creation logic
        # Here you would typically:
        # - Upload the workflow data to GCS
        # - Create database records
        # - Process the transformed nodes as needed
        
        return {
            "success": True,
            "message": "Workflow data transformed successfully",
            "transformed_nodes": transformed_nodes,
            "mcp_nodes": mcp_info,
            "component_nodes": component_info,
            "total_nodes": len(transformed_nodes)
        }
        
    except json.JSONDecodeError as e:
        print(f"[DEBUG] JSON parsing failed: {str(e)}")
        return {
            "success": False,
            "message": "Invalid JSON format in workflow_data",
            "error": str(e)
        }
    except Exception as e:
        print(f"[DEBUG] Unexpected error in workflow transformation: {str(e)}")
        return {
            "success": False,
            "message": f"Workflow transformation failed: {str(e)}",
            "error": str(e)
        }


def example_usage():
    """
    Example usage of the workflow transformation function.
    """
    
    # Example workflow data as it would come from the request
    sample_workflow_json = json.dumps({
        "nodes": [
            {
                "id": "start-node",
                "type": "WorkflowNode",
                "data": {
                    "type": "component",
                    "originalType": "StartNode",
                    "definition": {
                        "name": "StartNode",
                        "display_name": "Start"
                    }
                }
            },
            {
                "id": "MCP_Candidate_Interview_generate_interview_agenda-1747901643530",
                "type": "WorkflowNode",
                "data": {
                    "type": "mcp",
                    "originalType": "MCP_Candidate_Interview_generate_interview_agenda",
                    "definition": {
                        "name": "MCP_Candidate_Interview_generate_interview_agenda",
                        "display_name": "Candidate Interview - generate_interview_agenda",
                        "mcp_info": {
                            "server_id": "47d735d3-cd2b-4b40-9921-e0946c94dc31",
                            "input_schema": {
                                "type": "object",
                                "properties": {
                                    "resume_details": {
                                        "type": "string",
                                        "description": "Candidate's resume"
                                    },
                                    "jd_details": {
                                        "type": "string",
                                        "description": "Job description"
                                    }
                                },
                                "required": ["jd_details", "resume_details"]
                            },
                            "output_schema": {
                                "type": "object",
                                "properties": {
                                    "interview_agenda": {
                                        "type": "string",
                                        "description": "Generated interview agenda"
                                    }
                                },
                                "required": []
                            }
                        }
                    }
                }
            },
            {
                "id": "SelectDataComponent-1748238994773",
                "type": "WorkflowNode",
                "data": {
                    "type": "component",
                    "originalType": "SelectDataComponent",
                    "definition": {
                        "name": "SelectDataComponent",
                        "display_name": "Select Data"
                    }
                }
            }
        ]
    })
    
    # Process the workflow data
    result = example_create_workflow_with_transformation(sample_workflow_json)
    
    print("=== Example Usage Result ===")
    print(f"Success: {result['success']}")
    print(f"Message: {result['message']}")
    
    if result['success']:
        print(f"Total nodes processed: {result['total_nodes']}")
        print(f"MCP nodes: {len(result['mcp_nodes'])}")
        print(f"Component nodes: {len(result['component_nodes'])}")
        
        print("\nMCP Nodes:")
        for mcp in result['mcp_nodes']:
            print(f"  - {mcp['name']} (ID: {mcp['id']})")
        
        print("\nComponent Nodes:")
        for comp in result['component_nodes']:
            print(f"  - {comp['name']} ({comp['display_name']})")


if __name__ == "__main__":
    example_usage()
