# Workflow Data Transformation Guide

This guide explains how to use the workflow data transformation function to convert workflow data from the `sample_workflow.json` schema to the expected output format.

## Overview

The transformation function takes workflow data in the format received from the frontend (as seen in `sample_workflow.json`) and converts it to the expected format (as specified in `expected_json.json`).

## Files Created

1. **`app/utils/workflow_data_transformer.py`** - Main transformation functions
2. **`app/utils/workflow_integration_example.py`** - Example integration
3. **`tests/test_workflow_data_transformer.py`** - Test cases

## Main Function

### `transform_workflow_data_to_expected_format(workflow_data: Dict[str, Any]) -> List[Dict[str, Any]]`

This is the main function that transforms the workflow data.

**Input**: Workflow data in the format from `sample_workflow.json`
**Output**: List of transformed nodes in the expected format

## Usage in createWorkflow Function

Here's how to integrate this in your `createWorkflow` function:

```python
import json
from app.utils.workflow_data_transformer import transform_workflow_data_to_expected_format

def createWorkflow(self, request, context):
    try:
        # Parse workflow_data from string to JSON (existing code)
        workflow_data = json.loads(request.workflow_data)
        
        # NEW: Transform the workflow data to expected format
        transformed_nodes = transform_workflow_data_to_expected_format(workflow_data)
        
        # Now you can work with transformed_nodes in the expected format
        # Each node will have the structure:
        # For MCP nodes:
        # {
        #     "name": "MCP_Candidate_Interview_generate_interview_agenda",
        #     "id": "47d735d3-cd2b-4b40-9921-e0946c94dc31",
        #     "type": "mcp",
        #     "data": {
        #         "display_name": "Candidate Interview - generate_interview_agenda",
        #         "input_schema": {...},
        #         "output_schema": {...}
        #     }
        # }
        #
        # For component nodes:
        # {
        #     "name": "SelectDataComponent",
        #     "display_name": "Select Data",
        #     "type": "component"
        # }
        
        # Continue with your existing workflow creation logic...
        
    except json.JSONDecodeError as e:
        # Handle JSON parsing errors
        pass
```

## Helper Functions

### Extract Specific Node Types

```python
from app.utils.workflow_data_transformer import extract_mcp_nodes_only, extract_component_nodes_only

# Get only MCP nodes
mcp_nodes = extract_mcp_nodes_only(workflow_data)

# Get only component nodes  
component_nodes = extract_component_nodes_only(workflow_data)
```

## Expected Output Format

### MCP Node Output
```json
{
    "name": "MCP_Candidate_Interview_generate_interview_agenda",
    "id": "47d735d3-cd2b-4b40-9921-e0946c94dc31",
    "type": "mcp",
    "data": {
        "display_name": "Candidate Interview - generate_interview_agenda",
        "input_schema": {
            "type": "object",
            "properties": {
                "resume_details": {
                    "type": "string",
                    "description": "Candidate's resume"
                },
                "jd_details": {
                    "type": "string",
                    "description": "Job description"
                }
            },
            "required": ["jd_details", "resume_details"]
        },
        "output_schema": {
            "type": "object",
            "properties": {
                "interview_agenda": {
                    "type": "string",
                    "description": "Generated interview agenda"
                }
            },
            "required": []
        }
    }
}
```

### Component Node Output
```json
{
    "name": "SelectDataComponent",
    "display_name": "Select Data",
    "type": "component"
}
```

## Features

1. **Filters out system nodes**: StartNode and EndNode are automatically excluded
2. **Handles MCP nodes**: Extracts server_id, input_schema, output_schema from mcp_info
3. **Handles component nodes**: Extracts name and display_name
4. **Error handling**: Gracefully handles malformed data
5. **Type safety**: Includes type hints for better development experience

## Testing

Run the tests to verify the transformation works correctly:

```bash
# Simple test
cd /path/to/project
python -c "
import sys
sys.path.append('.')
from app.utils.workflow_data_transformer import transform_workflow_data_to_expected_format
# Test with sample data...
"

# Or run the integration example
python -c "
import sys
sys.path.append('.')
exec(open('app/utils/workflow_integration_example.py').read())
"
```

## Integration Steps

1. **Import the function** in your workflow service:
   ```python
   from app.utils.workflow_data_transformer import transform_workflow_data_to_expected_format
   ```

2. **Add transformation after JSON parsing**:
   ```python
   workflow_data = json.loads(request.workflow_data)
   transformed_nodes = transform_workflow_data_to_expected_format(workflow_data)
   ```

3. **Use the transformed data** for your workflow processing logic

4. **Handle the expected format** in your downstream processing

## Error Handling

The function includes robust error handling:
- Returns empty list for invalid input
- Skips malformed nodes gracefully
- Handles missing fields with default values

## Next Steps

1. Integrate this function into your `createWorkflow` implementation
2. Update any downstream processing to work with the expected format
3. Add additional node type support if needed
4. Consider adding validation for the transformed output
